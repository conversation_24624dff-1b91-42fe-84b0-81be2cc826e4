{"name": "@elizaos/client-direct", "version": "0.1.7-alpha.2", "main": "dist/index.js", "type": "module", "types": "dist/index.d.ts", "dependencies": {"@elizaos/core": "workspace:*", "@elizaos/plugin-image-generation": "workspace:*", "@elizaos/client-twitter": "workspace:*", "agent-linkedin-client": "workspace:*", "agent-instagram-client": "workspace:*", "@anthropic-ai/sdk": "^0.32.1", "@types/body-parser": "1.19.5", "@types/cors": "2.8.17", "@types/express": "5.0.0", "body-parser": "1.20.3", "node-forge": "^1.3.1", "cors": "2.8.5", "discord.js": "14.16.3", "express": "4.21.1", "multer": "1.4.5-lts.1", "@neplex/vectorizer": "^0.0.5"}, "devDependencies": {"tsup": "8.3.5", "@types/multer": "^1.4.12"}, "scripts": {"build": "tsup --format esm --dts", "dev": "tsup --format esm --dts --watch", "lint": "eslint --fix  --cache ."}, "peerDependencies": {"whatwg-url": "7.1.0"}}